<?php
// 测试音乐数据插入脚本
require_once 'config.php';
require_once 'source/include/mysql.php';

$DB = new MySQL(DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME, DB_CHARSET, TABLE_PREFIX, DB_PCONNECT);

// 检查音乐分类是否存在
$query = $DB->query("SELECT * FROM dir_categories WHERE cate_id = 286");
if ($DB->num_rows($query) == 0) {
    echo "音乐分类不存在，正在创建...\n";
    
    // 创建音乐分类
    $DB->query("INSERT INTO dir_categories (cate_id, root_id, cate_mod, cate_name, cate_dir, cate_keywords, cate_description, cate_arrparentid, cate_arrchildid, cate_postcount) VALUES 
    (286, 5, 'article', '音乐', 'music', '音乐,歌曲,mp3,在线听歌', '音乐文章分类', '0,5', '286', 0)");
    
    echo "音乐分类创建完成\n";
} else {
    echo "音乐分类已存在\n";
}

// 创建一些测试音乐文章
$test_articles = array(
    array(
        'title' => '经典老歌推荐 - 邓丽君经典歌曲',
        'content' => '邓丽君的经典歌曲《月亮代表我的心》在线试听：<br>
        <audio controls><source src="https://music.163.com/song/media/outer/url?id=347230" type="audio/mpeg"></audio><br>
        音乐外链：https://music.163.com/song/media/outer/url?id=347230<br>
        这首歌是华语乐坛的经典之作，深受广大听众喜爱。',
        'tags' => '邓丽君,经典老歌,月亮代表我的心'
    ),
    array(
        'title' => '流行音乐推荐 - 周杰伦热门歌曲',
        'content' => '周杰伦的《青花瓷》在线播放：<br>
        <audio controls><source src="https://y.qq.com/n/yqq/song/003a1tne1nSz1Y.html" type="audio/mpeg"></audio><br>
        QQ音乐链接：https://y.qq.com/n/yqq/song/003a1tne1nSz1Y.html<br>
        这首歌融合了中国古典文化元素，是周杰伦的代表作之一。',
        'tags' => '周杰伦,青花瓷,流行音乐'
    ),
    array(
        'title' => '网络歌曲精选 - 抖音热门音乐',
        'content' => '最近在抖音很火的歌曲《海底》：<br>
        音乐播放：<audio controls><source src="https://www.kugou.com/song/123456.mp3" type="audio/mpeg"></audio><br>
        酷狗音乐：https://www.kugou.com/song/123456.mp3<br>
        这首歌在短视频平台非常受欢迎，旋律优美动听。',
        'tags' => '抖音热歌,海底,网络歌曲'
    ),
    array(
        'title' => '轻音乐推荐 - 钢琴曲欣赏',
        'content' => '优美的钢琴曲《卡农》：<br>
        在线试听：<audio controls><source src="https://music.example.com/canon.mp3" type="audio/mpeg"></audio><br>
        下载链接：https://music.example.com/canon.mp3<br>
        这是一首非常经典的钢琴曲，适合放松心情时聆听。',
        'tags' => '钢琴曲,卡农,轻音乐'
    ),
    array(
        'title' => '民谣音乐 - 吉他弹唱推荐',
        'content' => '民谣歌曲《成都》吉他弹唱版：<br>
        音频文件：<audio controls><source src="https://music.demo.com/chengdu.wav" type="audio/wav"></audio><br>
        WAV格式：https://music.demo.com/chengdu.wav<br>
        这首歌描述了对成都这座城市的眷恋之情。',
        'tags' => '民谣,成都,吉他弹唱'
    )
);

// 插入测试文章
foreach ($test_articles as $article) {
    $title = addslashes($article['title']);
    $content = addslashes($article['content']);
    $tags = addslashes($article['tags']);
    $intro = addslashes(substr(strip_tags($article['content']), 0, 100));
    $ctime = time();
    
    $sql = "INSERT INTO dir_articles (cate_id, art_title, art_content, art_tags, art_intro, art_status, art_ctime) 
            VALUES (286, '$title', '$content', '$tags', '$intro', 3, $ctime)";
    
    if ($DB->query($sql)) {
        echo "插入文章：{$article['title']} - 成功\n";
    } else {
        echo "插入文章：{$article['title']} - 失败\n";
    }
}

echo "\n测试数据创建完成！\n";

// 测试音乐链接提取功能
echo "\n测试音乐链接提取：\n";
require_once 'source/module/article.php';

$music_links = get_music_links(286, 10);
echo "找到 " . count($music_links) . " 个音乐链接：\n";
foreach ($music_links as $link) {
    echo "- {$link['title']}: {$link['url']}\n";
}
?>
